import { app, shell, BrowserWindow, ipcMain } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?asset'

function createWindow(): void {
  // Create the browser window with modern native styling
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    show: false,
    autoHideMenuBar: true,

    // 现代化窗口样式配置
    titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'hidden',
    frame: false, // 移除默认边框
    transparent: false,
    backgroundColor: '#1a1a1a', // 深色背景

    // Windows 特定的现代化效果
    ...(process.platform === 'win32' && {
      backgroundMaterial: 'mica', // Windows 11 Mica 效果
      roundedCorners: true, // 圆角窗口
    }),

    // macOS 特定效果
    ...(process.platform === 'darwin' && {
      vibrancy: 'under-window',
      visualEffectState: 'active',
    }),

    // 窗口图标
    ...(process.platform === 'linux' ? { icon } : {}),

    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      contextIsolation: true,
      enableRemoteModule: false,
      nodeIntegration: false,
    },
  })

  // 添加自定义窗口控制按钮和标题栏
  setupCustomTitleBar(mainWindow)

  mainWindow.on('ready-to-show', () => {
    // 添加现代化的窗口动画
    if (process.platform === 'win32') {
      mainWindow.setOpacity(0)
      mainWindow.show()

      let opacity = 0
      const fadeIn = setInterval(() => {
        opacity += 0.05
        mainWindow.setOpacity(opacity)
        if (opacity >= 1) {
          clearInterval(fadeIn)
        }
      }, 16)
    } else {
      mainWindow.show()
    }

    // 添加窗口阴影效果 (仅限 Windows)
    if (process.platform === 'win32') {
      mainWindow.setHasShadow(true)
    }
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// 设置自定义标题栏
function setupCustomTitleBar(window: BrowserWindow): void {
  // 注入自定义标题栏样式和控制逻辑
  window.webContents.on('dom-ready', () => {
    window.webContents.insertCSS(`
      /* 自定义标题栏样式 */
      body {
        margin: 0;
        padding-top: 32px; /* 为标题栏留出空间 */
      }

      /* 创建自定义标题栏 */
      body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        height: 32px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-app-region: drag;
        z-index: 9999;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px) saturate(180%);
        box-shadow: 0 1px 10px rgba(0, 0, 0, 0.1);
      }

      /* Windows 控制按钮区域 */
      .window-controls {
        position: fixed;
        top: 0;
        right: 0;
        height: 32px;
        display: flex;
        z-index: 10000;
        -webkit-app-region: no-drag;
      }

      .window-control-button {
        width: 46px;
        height: 32px;
        border: none;
        background: transparent;
        color: white;
        font-size: 10px;
        cursor: pointer;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: 'Segoe UI', sans-serif;
        position: relative;
        overflow: hidden;
      }

      .window-control-button::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.1);
        opacity: 0;
        transition: opacity 0.2s ease;
      }

      .window-control-button:hover::before {
        opacity: 1;
      }

      .window-control-button:hover {
        transform: scale(1.05);
      }

      .window-control-button.close:hover {
        background-color: #e81123;
        color: white;
      }

      .window-control-button.close:hover::before {
        background-color: rgba(255, 255, 255, 0.2);
      }

      .window-control-button.maximize:hover {
        background-color: rgba(255, 255, 255, 0.15);
      }

      /* 应用标题 */
      .app-title {
        position: fixed;
        top: 0;
        left: 12px;
        height: 32px;
        line-height: 32px;
        color: white;
        font-size: 14px;
        font-weight: 500;
        z-index: 10000;
        -webkit-app-region: no-drag;
        pointer-events: none;
        font-family: 'Segoe UI', sans-serif;
      }

      /* 现代化滚动条 */
      ::-webkit-scrollbar {
        width: 8px;
      }

      ::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
      }

      ::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    `)

    // 注入窗口控制按钮
    window.webContents.executeJavaScript(`
      // 创建标题栏元素
      const titleBar = document.createElement('div');
      titleBar.className = 'app-title';
      titleBar.textContent = 'Modern Electron App';
      document.body.appendChild(titleBar);

      // 创建窗口控制按钮
      const controls = document.createElement('div');
      controls.className = 'window-controls';

      const minimizeBtn = document.createElement('button');
      minimizeBtn.className = 'window-control-button minimize';
      minimizeBtn.innerHTML = '−';
      minimizeBtn.title = 'Minimize';
      minimizeBtn.onclick = () => window.electron.ipcRenderer.send('window-minimize');

      const maximizeBtn = document.createElement('button');
      maximizeBtn.className = 'window-control-button maximize';
      maximizeBtn.innerHTML = '□';
      maximizeBtn.title = 'Maximize';
      maximizeBtn.onclick = () => window.electron.ipcRenderer.send('window-maximize');

      const closeBtn = document.createElement('button');
      closeBtn.className = 'window-control-button close';
      closeBtn.innerHTML = '×';
      closeBtn.title = 'Close';
      closeBtn.onclick = () => window.electron.ipcRenderer.send('window-close');

      controls.appendChild(minimizeBtn);
      controls.appendChild(maximizeBtn);
      controls.appendChild(closeBtn);
      document.body.appendChild(controls);
    `)
  })
}

// Setup IPC handlers
function setupIpcHandlers(): void {
  // 窗口控制 IPC 处理
  ipcMain.on('window-minimize', (event) => {
    const window = BrowserWindow.fromWebContents(event.sender)
    window?.minimize()
  })

  ipcMain.on('window-maximize', (event) => {
    const window = BrowserWindow.fromWebContents(event.sender)
    if (window?.isMaximized()) {
      window.restore()
    } else {
      window?.maximize()
    }
  })

  ipcMain.on('window-close', (event) => {
    const window = BrowserWindow.fromWebContents(event.sender)
    window?.close()
  })

  // Legacy IPC handlers
  ipcMain.on('ping', () => console.log('pong'))
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron.modernapp')

  // Setup IPC handlers
  setupIpcHandlers()

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
