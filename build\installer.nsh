!macro customInstall
  ; —— 安装时：禁用边缘滑动（策略键 AllowEdgeSwipe = 0）——
  ; 双写 64/32 视图，兼容性更好
  SetRegView 64
  WriteRegDWORD HKLM "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "AllowEdgeSwipe" 0
  ; 如需仅限制当前登录用户，可改写/追加 HKCU（按需取消注释）
  ; WriteRegDWORD HKCU "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "AllowEdgeSwipe" 0

  SetRegView 32
  WriteRegDWORD HKLM "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "AllowEdgeSwipe" 0
  ; WriteRegDWORD HKCU "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "AllowEdgeSwipe" 0

  ; （可选）提示：部分机器需注销/重启系统后手势才完全生效
  ; MessageBox MB_OK "已禁用系统边缘滑动。为确保生效，建议注销或重启 Windows。"
!macroend

!macro customUnInstall
  ; —— 卸载时：恢复/清理 —— 任选其一（保留其一，注释掉另一个）：

  ; 方案A：恢复为允许（1）
  SetRegView 64
  WriteRegDWORD HKLM "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "AllowEdgeSwipe" 1
  SetRegView 32
  WriteRegDWORD HKLM "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "AllowEdgeSwipe" 1

  ; 方案B：彻底删除该值（让系统回到默认或受域策略控制）
  ; SetRegView 64
  ; DeleteRegValue HKLM "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "AllowEdgeSwipe"
  ; SetRegView 32
  ; DeleteRegValue HKLM "SOFTWARE\Policies\Microsoft\Windows\EdgeUI" "AllowEdgeSwipe"

  ; 如安装时写过 HKCU，也可对 HKCU 执行同样恢复/删除（按需）
!macroend
