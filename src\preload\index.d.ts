import { ElectronAPI } from '@electron-toolkit/preload'

interface CustomAPI {
  // Configuration APIs
  getConfig: () => Promise<any>
  updateConfig: (updates: any) => Promise<any>
  resetConfig: () => Promise<any>
  exportConfig: (filePath: string) => Promise<void>
  importConfig: (filePath: string) => Promise<any>

  // Theme APIs
  getAvailableThemes: () => Promise<any>
  applyTheme: (theme: any) => Promise<void>

  // Dialog APIs
  showMessageBox: (options: any) => Promise<any>
  showSaveDialog: (options: any) => Promise<any>
  showOpenDialog: (options: any) => Promise<any>

  // Event listeners
  onThemeChanged: (callback: (theme: any) => void) => void
  onOpenSettings: (callback: () => void) => void

  // Remove listeners
  removeAllListeners: (channel: string) => void
}

declare global {
  interface Window {
    electron: ElectronAPI
    api: CustomAPI
  }
}
